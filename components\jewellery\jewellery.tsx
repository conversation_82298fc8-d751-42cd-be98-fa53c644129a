"use client";
import React, { useCallback, useState } from 'react'
import Footer from "../home/<USER>";
import Navbar from "./navbar";
import JewelleryProductList from "./product-listing";
import Loader from "../../components/common/loader"
import Image from "next/image";
import { useRouter } from "next/navigation";
import { poppins } from "@/src/common/helper";
import { IoIosArrowDown } from "react-icons/io";
import SearchBar from "../common/searchBar";
import { debounce } from "lodash";

const Jewellery = () => {
    const router = useRouter();
    const [search, setSearch] = useState("");
    const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(search);

    const handleSearch = useCallback(
        debounce((query: any) => {
            setDebouncedSearchQuery(query);
            // fecthList(1, query);
        }, 500),
        []
    );

    return (
        <div className={`w-auto h-auto space-y-14 select-none ${poppins.className}`}>
            {/* Section 1: Hero Section */}
            <div className="relative w-full h-[90vh]">
                {/* Navbar on top of image */}
                <div className="absolute top-0 left-0 w-full z-10">
                    <Navbar />
                </div>

                {/* Hero Image */}
                <Image
                    src="/assets/jewellery/banner.svg"
                    alt="Shreeji Gems Logo"
                    fill
                    className="object-cover"
                    priority
                />
            </div>

            {/* Section 2: Filter, Search & Sort */}
            <div className="flex justify-between items-cente gap-6 px-10">
                {/* Filter */}
                <div className="flex border-2 border-black rounded-[50px] px-5 justify-center items-center">
                    <Image
                        src="/assets/jewellery/filter-icon.svg"
                        alt="Shreeji Gems Logo"
                        width={24}
                        height={24}
                        className=""
                    />
                    <p className="px-2 font-[500]">Filter</p>
                    <IoIosArrowDown className="h-[20px] w-auto cursor-pointer mt-1" />
                </div>

                {/* Search */}
                <div className="flex min-w-[550px] border-2 border-black rounded-[50px] px-3 py-1 justify-center items-center">
                    <SearchBar {...{ search, setSearch, handleSearch }} />
                </div>

                {/* Sort */}
                <div className="flex border-2 border-black rounded-[50px] px-5 justify-center items-center">
                    <Image
                        src="/assets/jewellery/filter-icon.svg"
                        alt="Shreeji Gems Logo"
                        width={24}
                        height={24}
                        className=""
                    />
                    <p className="px-2 font-[500]">Sort</p>
                    <IoIosArrowDown className="h-[20px] w-auto cursor-pointer mt-1" />
                </div>
            </div>

            {/* Section 3: Jewellery Items */}
            <div>
                <JewelleryProductList />
            </div>

            {/* Section 4: More loader */}
            <Loader />

            {/* Section : Footer Section */}
            <Footer />
        </div>
    )
}

export default Jewellery
