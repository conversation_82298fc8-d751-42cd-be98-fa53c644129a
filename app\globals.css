@import "tailwindcss";
@tailwind utilities;

:root {
  --background: #ffffff; /* White background */
  --foreground: #171717; /* Dark text */
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

@font-face {
  font-family: "Futura Bk BT";
  src: url("/public/fonts/FuturaBkBT.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

body {
  font-family: "Futura Bk BT", sans-serif;
}

/* Animation for fade-in */
@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: scale(0.98);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
}

