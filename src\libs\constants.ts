export const COLLECTION_TYPE = [
  { value: "RING", label: "Ring" },
  { value: "EARRING", label: "Earring" },
  { value: "NECKLACE", label: "Necklace" },
  { value: "BRACELET", label: "Bracelet" },
  { value: "DIAMONDS", label: "Diamonds" },
];

export const JEWELLERY_TYPE = [
  { value: "RING", label: "Ring" },
  { value: "NECKLACE", label: "Necklace" },
  { value: "EARRING", label: "Earring" },
  { value: "BRACELET", label: "Bracelet" },
  { value: "GIFT", label: "Gift" },
  { value: "COLLECTION", label: "Collection" },
  { value: "BESPOKE_CREATIONS", label: "Bespoke creations" },
];

export const EARRING_TYPE = [
  {
    value: "STUDS",
    label: "Studs",
    types: [
      { value: "SOLITAIRE_STUDS", label: "Solitaire Studs" },
      { value: "<PERSON>AL<PERSON>", label: "Halo" },
      { value: "CLUSTER", label: "Cluster" },
    ],
  },
  {
    value: "HOOPS",
    label: "Hoops",
    types: [
      { value: "CLASSIC_HOOPS", label: "Classic Hoops" },
      { value: "CHUNKY_HOOP", label: "Chunky Hoop" },
      { value: "CLUSTER_HOOPS", label: "Cluster Hoops" },
      { value: "HUGGIES", label: "Huggies" },
    ],
  },
  {
    value: "DROPS_AND_DANGLES",
    label: "Drops & Dangles",
    types: [
      { value: "LARIAT", label: "Lariat" },
      { value: "TEARDROP", label: "Teardrop" },
      { value: "CHANDELIER", label: "Chandelier" },
    ],
  },
  {
    value: "CUFF_AND_CLIMBER",
    label: "Cuff & Climber Earrings",
    types: [],
  },
  {
    value: "THREADER",
    label: "Threader Earrings",
    types: [],
  },
  {
    value: "JACKET",
    label: "Jacket Earrings",
    types: [],
  },
  {
    value: "CHAIN",
    label: "Chain Earrings",
    types: [],
  },
];

export const GIFT_TYPE = [
  { value: "BIRTHDAY_GIFT", label: "Birthday Gift" },
  { value: "ANNIVERSARY_GIFT", label: "Anniversary Gift" },
  { value: "SURPRISE_HER", label: "Surprise Her" },
  { value: "SURPRISE_HIM", label: "Surprise Him" },
  { value: "OCCASIONAL_GIFT", label: "Occasional Gift" },
  { value: "CORPORATE_GIFT", label: "Corporate Gift" },
  { value: "CUSTOMIZED_GIFT", label: "Customize Gift from Your Heart" },
];

export const COLLECTION_NEW_TYPE = [
  { value: "NEW_ARRIVAL", label: "New Arrival" },
  { value: "FLORAL", label: "Floral" },
  { value: "DIARY", label: "Diary" },
  { value: "MINIMALIST", label: "Minimalist" },
  { value: "FRESH", label: "Fresh" },
  { value: "NATURE_INSPIRED", label: "Nature Inspired" },
];

export const RING_TYPE = [
  {
    value: "ENGAGEMENT_RING",
    label: "Engagement Ring",
    types: [
      { value: "SOLITAIRE", label: "Solitaire" },
      { value: "HALO", label: "Halo" },
      { value: "TRILOGY", label: "Trilogy" },
      { value: "PAVE", label: "Pave" },
      { value: "CLUSTER", label: "Cluster" },
      { value: "MULTI_STONE", label: "Multi-Stone" },
      { value: "VINTAGE", label: "Vintage" },
    ],
  },
  {
    value: "WEDDING_RING",
    label: "Wedding Ring",
    types: [
      { value: "BANDS", label: "Bands" },
      { value: "ETERNITY", label: "Eternity" },
      { value: "CONTOUR", label: "Contour" },
      { value: "OPEN_BANDS", label: "Open Bands" },
      { value: "PAVE", label: "Pave" },
      { value: "MULTI_STONE", label: "Multi-Stone" },
    ],
  },
  {
    value: "EVERYDAY_FASHION_RING",
    label: "Everyday Fashion Ring",
    types: [
      { value: "COCKTAIL_RINGS", label: "Cocktail Rings" },
      { value: "STACKING_RINGS", label: "Stacking Rings" },
      { value: "STATEMENT_RINGS", label: "Statement Rings" },
      { value: "SIGNET_RINGS", label: "Signet Rings" },
      { value: "CLUSTER_RINGS", label: "Cluster Rings" },
      { value: "CHAIN_RING", label: "Chain Ring" },
      { value: "INSPIRED_RING", label: "Inspired Ring" },
    ],
  },
];

export const BRACELET_TYPE = [
  { value: "CHAIN", label: "Chain" },
  { value: "TENNIS", label: "Tennis" },
  { value: "LINK", label: "Link" },
  { value: "CUFF", label: "Cuff" },
  { value: "STATEMENT", label: "Statement" },
  { value: "BANGLE", label: "Bangle" },
  { value: "CORD", label: "Cord" },
  { value: "CHARM", label: "Charm" },
];


export const CATEGORY_TYPE = [
  {
    value: "OUR_LEGACY",
    label: "Our Legacy",
    selected_text: "OUR LEGACY",
    description:
      "Since our inception, Shreeji Gems has stood for trust, craftsmanship, and innovation in the world of fine jewellery and diamonds. With decades of experience and generations of expertise.we have proudly served retailers and wholesalers across the globe—combining heritage with cutting-edge technology to deliver brilliance in every piece.",
    img: "/assets/home/<USER>/Our Legacy.svg",
  },
  {
    value: "OUR_CRAFTSMANSHIP",
    label: "Our Craftsmanship",
    selected_text: "OUR CRAFTSMANSHIP",
    description:
      "Each jewel is more than a product—it’s a promise. Our in-house artisans and designers transform raw materials into masterpieces using a blend of traditional techniques and modern design tools.From the cut of a diamond to the polish of gold, we ensure unmatched quality and precision at every stage.",
    img: "/assets/home/<USER>/Our Craftsmanship.svg",
  },
  {
    value: "YOUR_TRUSTED_JEWELLERY_PARTNER",
    label: "Your Trusted Jewellery Partner",
    selected_text: "YOUR TRUSTED JEWELLERY PARTNER",
    description:
      "We offer end-to-end diamond and jewellery solutions — from raw materials to custom designed finished pieces. With unmatched variety, quality, and integrity, we ensure excellence at every stage.",
    img: "/assets/home/<USER>/Your Trusted Jewellery Partner.svg",
  },
  {
    value: "CRAFTING_TRUST_WITH_QUALITY",
    label: "Crafting Trust with Quality",
    selected_text: "CRAFTING TRUST WITH QUALITY",
    description:
      "We offer a diverse range of diamonds and jewellery, tailored to every style and need. From raw materials to finished masterpieces, we provide complete, custom-crafted solutions.",
    img: "/assets/home/<USER>/Crafting Trust with Quality.svg",
  },
];

export const PRODUCT_LIST = [
  {
    value: "P1",
    image: "/assets/jewellery/product-sample.svg",
    type: "RING",
    skuId: "SKU-RNG-001",
    price: "₹250.00",
    isInCart: false,
  },
  {
    value: "P2",
    image: "/assets/jewellery/product-sample.svg",
    type: "NECKLACE",
    skuId: "SKU-NCK-002",
    price: "₹1,250.00",
    isInCart: false,
  },
  {
    value: "P3",
    image: "/assets/jewellery/product-sample.svg",
    type: "EARRING",
    skuId: "SKU-ER-003",
    price: "₹850.00",
    isInCart: false,
  },
  {
    value: "P4",
    image: "/assets/jewellery/product-sample.svg",
    type: "BRACELET",
    skuId: "SKU-BRC-004",
    price: "₹1,100.00",
    isInCart: true,
  },
  {
    value: "Zig-prodct",
    image: "/assets/jewellery/product.svg",
    type: "BRACELET",
  },
  {
    value: "P5",
    image: "/assets/jewellery/product-sample.svg",
    type: "GIFT",
    skuId: "SKU-GFT-005",
    price: "₹499.00",
    isInCart: true,
  },
  {
    value: "P6",
    image: "/assets/jewellery/product-sample.svg",
    type: "COLLECTION",
    skuId: "SKU-CLT-006",
    price: "₹1,999.00",
    isInCart: false,
  },
  {
    value: "P7",
    image: "/assets/jewellery/product-sample.svg",
    type: "BESPOKE_CREATIONS",
    skuId: "SKU-BSK-007",
    price: "₹2,500.00",
    isInCart: false,
  },
  {
    value: "P8",
    image: "/assets/jewellery/product-sample.svg",
    type: "RING", // repeat type if needed
    skuId: "SKU-RNG-008",
    price: "₹299.00",
    isInCart: true,
  },
];
