import React from 'react'
import Image from "next/image";
import { poppins } from '@/src/common/helper';
import { CgProfile } from "react-icons/cg";
import { IoHeartOutline } from "react-icons/io5";
import { PiHandbag } from "react-icons/pi";
import { JEWELLERY_TYPE } from "@/src/libs/constants"

const Navbar = () => {
    return (
        <div className={`flex justify-center items-center w-auto h-auto px-5 pt-5 space-y-14 select-none text-black backdrop-blur-sm ${poppins.className}`}
            style={{ background: "rgba(0, 0, 0, 0.10)" }}
        >
            {/* Logo and Some buttons */}
            <div className="w-full flex flex-col justify-center items-center">
                <div className="relative w-full flex flex-col justify-center items-center">
                    <Image
                        src="/assets/home/<USER>"
                        width={151}
                        height={68}
                        alt="Shreeji Gems Logo"
                        data-aos="zoom-in"
                    />
                    <div className="absolute right-6 flex justify-center items-center space-x-4">
                        <IoHeartOutline className="h-[25px] w-auto cursor-pointer" title="Wishlist" />
                        <PiHandbag className="h-[25px] w-auto cursor-pointer" title="Cart" />
                        <CgProfile className="h-[25px] w-auto cursor-pointer" title="Profile" />
                    </div>
                </div>

                <div className="flex justify-center items-center space-x-14 text-black">
                    {/* {JEWELLERY_TYPE.map((item) => (
                        <div
                            key={item.value}
                            className="cursor-pointer text-[16px] font-[400] hover:scale-110 ease-in-out transition-all duration-300"
                        >
                            {item.label}
                        </div>
                    ))} */}
                    <div
                        className="cursor-pointer text-[16px] font-[400] hover:scale-110 ease-in-out transition-all duration-300"
                    >
                        Ring
                    </div>
                    <div
                        className="cursor-pointer text-[16px] font-[400] hover:scale-110 ease-in-out transition-all duration-300"
                    >
                        Necklace
                    </div>
                    <div
                        className="cursor-pointer text-[16px] font-[400] hover:scale-110 ease-in-out transition-all duration-300"
                    >
                        Earring
                    </div>
                    <div
                        className="cursor-pointer text-[16px] font-[400] hover:scale-110 ease-in-out transition-all duration-300"
                    >
                        Bracelet
                    </div>
                    <div
                        className="cursor-pointer text-[16px] font-[400] hover:scale-110 ease-in-out transition-all duration-300"
                    >
                        Gift
                    </div>
                    <div
                        className="cursor-pointer text-[16px] font-[400] hover:scale-110 ease-in-out transition-all duration-300"
                    >
                        Collection
                    </div>
                    <div
                        className="cursor-pointer text-[16px] font-[400] hover:scale-110 ease-in-out transition-all duration-300"
                    >
                        Bespoke creations
                    </div>
                    <Image
                        src="/assets/diamond/diamond.svg"
                        width={50}
                        height={100}
                        alt="Shreeji Gems Diamonds"
                        className="w-[70px] h-auto cursor-pointer  hover:scale-110 ease-in-out transition-all duration-500"
                    />
                </div>
            </div>
        </div>
    )
}

export default Navbar
