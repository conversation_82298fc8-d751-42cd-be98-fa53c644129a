"use client";
import React, { useState } from "react";
import Image from "next/image";
import { poppins } from "@/src/common/helper";
import { CgProfile } from "react-icons/cg";
import { IoHeartOutline } from "react-icons/io5";
import { PiHandbag } from "react-icons/pi";
import {
  JEWELLERY_TYPE,
  RING_TYPE,
  EARRING_TYPE,
  NECKLACE_TYPE,
  BRACELET_TYPE,
  GIFT_TYPE,
  COLLECTION_NEW_TYPE
} from "@/src/libs/constants";
import NavbarDropdown from "./navbar-dropdown";

const Navbar = () => {
    const [hoveredItem, setHoveredItem] = useState<string | null>(null);

    // Function to get dropdown items based on jewelry type
    const getDropdownItems = (jewelryType: string) => {
        switch (jewelryType) {
            case "RING":
                return RING_TYPE;
            case "EARRING":
                return EARRING_TYPE;
            case "NECKLACE":
                return NECKLACE_TYPE;
            case "BRACELET":
                return BRACELET_TYPE;
            case "GIFT":
                return GIFT_TYPE.map(item => ({ ...item, types: [] }));
            case "COLLECTION":
                return COLLECTION_NEW_TYPE.map(item => ({ ...item, types: [] }));
            default:
                return [];
        }
    };

    return (
        <div
            className={`relative flex justify-center items-center w-auto h-auto px-5 pt-5 space-y-14 select-none text-black backdrop-blur-sm ${poppins.className}`}
            style={{ background: "rgba(0, 0, 0, 0.10)" }}
            onMouseLeave={() => setHoveredItem(null)}
        >
            {/* Logo and Some buttons */}
            <div className="w-full flex flex-col justify-center items-center">
                <div className="relative w-full flex flex-col justify-center items-center">
                    <Image
                        src="/assets/home/<USER>"
                        width={151}
                        height={68}
                        alt="Shreeji Gems Logo"
                        data-aos="zoom-in"
                    />
                    <div className="absolute right-6 flex justify-center items-center space-x-4">
                        <IoHeartOutline className="h-[25px] w-auto cursor-pointer" title="Wishlist" />
                        <PiHandbag className="h-[25px] w-auto cursor-pointer" title="Cart" />
                        <CgProfile className="h-[25px] w-auto cursor-pointer" title="Profile" />
                    </div>
                </div>

                <div className="flex justify-center items-center space-x-14 text-black">
                    {JEWELLERY_TYPE.map((item) => (
                        <div
                            key={item.value}
                            className="relative cursor-pointer text-[16px] font-[400] hover:scale-110 ease-in-out transition-all duration-300"
                            onMouseEnter={() => setHoveredItem(item.value)}
                        >
                            {item.label}
                        </div>
                    ))}
                    <Image
                        src="/assets/diamond/diamond.svg"
                        width={50}
                        height={100}
                        alt="Shreeji Gems Diamonds"
                        className="w-[70px] h-auto cursor-pointer  hover:scale-110 ease-in-out transition-all duration-500"
                    />
                </div>
            </div>

            {/* Dropdown Menu */}
            <NavbarDropdown
                items={hoveredItem ? getDropdownItems(hoveredItem) : []}
                isVisible={hoveredItem !== null}
            />
        </div>
    );
};

export default Navbar;
